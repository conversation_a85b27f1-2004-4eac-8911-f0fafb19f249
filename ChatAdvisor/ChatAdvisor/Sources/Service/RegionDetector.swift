//
//  RegionDetector.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/1/29.
//

import Foundation
import StoreKit
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "RegionDetector")

/// 网络测速结果
struct NetworkSpeedResult {
    let endpoint: String
    let responseTime: TimeInterval // 毫秒
    let isSuccess: Bool
    let timestamp: Date
}

/// 地区检测结果
struct RegionDetectionResult {
    let isChina: Bool
    let confidence: Double // 0.0 - 1.0
    let sources: [String] // 检测来源
    let selectedEndpoint: String // 选择的端点
    let speedResults: [NetworkSpeedResult] // 测速结果
    let timestamp: Date
}

/// 智能端点选择器 - 基于地理位置和网络性能选择最优端点
class RegionDetector: ObservableObject {
    static let shared = RegionDetector()

    @Published var currentResult: RegionDetectionResult?

    // 缓存有效期：1小时（网络性能可能变化较快）
    private let cacheValidityDuration: TimeInterval = 60 * 60

    // 测速超时时间：3秒
    private let speedTestTimeout: TimeInterval = 3.0

    // 测速重试次数
    private let speedTestRetries: Int = 2

    private init() {}
    
    /// 智能选择最优端点
    /// - Parameter forceRefresh: 是否强制刷新，忽略缓存
    /// - Returns: 检测结果
    func detectOptimalEndpoint(forceRefresh: Bool = false) async -> RegionDetectionResult {
        // 检查缓存
        if !forceRefresh, let cachedResult = getCachedResult() {
            await MainActor.run {
                self.currentResult = cachedResult
            }
            return cachedResult
        }

        // 执行综合检测（地理位置 + 网络性能）
        let result = await performIntelligentDetection()

        // 缓存结果
        cacheResult(result)

        await MainActor.run {
            self.currentResult = result
        }

        return result
    }
    
    /// 执行智能检测（地理位置 + 网络性能）
    private func performIntelligentDetection() async -> RegionDetectionResult {
        var sources: [String] = []

        // 1. 地理位置检测
        let geoScore = await detectGeographicLocation()
        sources.append("地理位置检测")

        // 2. 网络性能测试
        let speedResults = await performSpeedTests()
        sources.append("网络性能测试")

        // 3. 综合决策
        let decision = makeIntelligentDecision(geoScore: geoScore, speedResults: speedResults)
        sources.append("综合决策算法")

        logger.info("智能端点选择完成 - 选择: \(decision.selectedEndpoint), 置信度: \(decision.confidence)")

        return RegionDetectionResult(
            isChina: decision.isChina,
            confidence: decision.confidence,
            sources: sources,
            selectedEndpoint: decision.selectedEndpoint,
            speedResults: speedResults,
            timestamp: Date()
        )
    }

    /// 检测地理位置倾向
    private func detectGeographicLocation() async -> Double {
        var score: Double = 0.0
        var factors: [String] = []

        // App Store Storefront (权重: 40%)
        if let countryCode = SKPaymentQueue.default().storefront?.countryCode {
            let isChina = countryCode == "CHN"
            score += (isChina ? 1.0 : 0.0) * 0.4
            factors.append("Storefront: \(countryCode)")
        }

        // 系统语言 (权重: 35%)
        let languageScore = detectLanguagePreference()
        score += languageScore * 0.35
        factors.append("语言偏好: \(Int(languageScore * 100))%")

        // 时区 (权重: 25%)
        let timezoneScore = detectTimezonePreference()
        score += timezoneScore * 0.25
        factors.append("时区: \(Int(timezoneScore * 100))%")

        logger.info("地理位置检测 - 评分: \(score), 因子: \(factors.joined(separator: ", "))")
        return score
    }

    /// 执行网络性能测试
    private func performSpeedTests() async -> [NetworkSpeedResult] {
        let endpoints = [
            ("sanva", "https://advisor.sanva.tk"),
            ("sanvaCn", "https://advisor.sanva.top")
        ]

        var results: [NetworkSpeedResult] = []

        // 并发测试两个端点
        await withTaskGroup(of: NetworkSpeedResult?.self) { group in
            for (name, url) in endpoints {
                group.addTask {
                    await self.testEndpointSpeed(name: name, url: url)
                }
            }

            for await result in group {
                if let result = result {
                    results.append(result)
                }
            }
        }

        // 按响应时间排序
        results.sort { $0.responseTime < $1.responseTime }

        logger.info("网络测速完成 - 结果: \(results.map { "\($0.endpoint): \(Int($0.responseTime))ms" }.joined(separator: ", "))")

        return results
    }

    /// 测试单个端点的速度
    private func testEndpointSpeed(name: String, url: String) async -> NetworkSpeedResult? {
        guard let testURL = URL(string: "\(url)/ping") else {
            // 如果没有专门的 ping 端点，使用根路径
            guard let rootURL = URL(string: url) else {
                return NetworkSpeedResult(
                    endpoint: name,
                    responseTime: Double.infinity,
                    isSuccess: false,
                    timestamp: Date()
                )
            }
            return await performSpeedTest(endpoint: name, url: rootURL)
        }

        return await performSpeedTest(endpoint: name, url: testURL)
    }

    /// 执行实际的速度测试
    private func performSpeedTest(endpoint: String, url: URL) async -> NetworkSpeedResult {
        let startTime = CFAbsoluteTimeGetCurrent()

        do {
            var request = URLRequest(url: url)
            request.httpMethod = "HEAD" // 使用 HEAD 请求减少数据传输
            request.timeoutInterval = speedTestTimeout
            request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData

            let (_, response) = try await URLSession.shared.data(for: request)

            let endTime = CFAbsoluteTimeGetCurrent()
            let responseTime = (endTime - startTime) * 1000 // 转换为毫秒

            let isSuccess = (response as? HTTPURLResponse)?.statusCode == 200

            return NetworkSpeedResult(
                endpoint: endpoint,
                responseTime: responseTime,
                isSuccess: isSuccess,
                timestamp: Date()
            )
        } catch {
            let endTime = CFAbsoluteTimeGetCurrent()
            let responseTime = (endTime - startTime) * 1000

            logger.warning("端点 \(endpoint) 测速失败: \(error.localizedDescription)")

            return NetworkSpeedResult(
                endpoint: endpoint,
                responseTime: responseTime,
                isSuccess: false,
                timestamp: Date()
            )
        }
    }

    /// 智能决策算法
    private func makeIntelligentDecision(geoScore: Double, speedResults: [NetworkSpeedResult]) -> (isChina: Bool, confidence: Double, selectedEndpoint: String) {

        // 如果没有有效的测速结果，回退到地理位置判断
        let validResults = speedResults.filter { $0.isSuccess }
        guard !validResults.isEmpty else {
            let isChina = geoScore > 0.5
            let endpoint = isChina ? "sanvaCn" : "sanva"
            logger.info("网络测速失败，回退到地理位置判断: \(endpoint)")
            return (isChina, abs(geoScore - 0.5) * 2, endpoint)
        }

        // 找到最快的端点
        let fastestResult = validResults.min(by: { $0.responseTime < $1.responseTime })!

        // 计算速度差异
        let sanvaResult = validResults.first { $0.endpoint == "sanva" }
        let sanvaCnResult = validResults.first { $0.endpoint == "sanvaCn" }

        var selectedEndpoint = fastestResult.endpoint
        var confidence: Double = 0.8 // 基础置信度

        // 如果两个端点都可用，进行综合判断
        if let sanvaTime = sanvaResult?.responseTime,
           let sanvaCnTime = sanvaCnResult?.responseTime {

            let speedDifference = abs(sanvaTime - sanvaCnTime)
            let speedThreshold: Double = 200 // 200ms 阈值

            // 如果速度差异很小，优先考虑地理位置
            if speedDifference < speedThreshold {
                let geoPreferChina = geoScore > 0.5
                selectedEndpoint = geoPreferChina ? "sanvaCn" : "sanva"
                confidence = 0.6 // 降低置信度，因为是基于地理位置的决策

                logger.info("速度差异较小(\(Int(speedDifference))ms)，基于地理位置选择: \(selectedEndpoint)")
            } else {
                // 速度差异明显，选择更快的端点
                selectedEndpoint = fastestResult.endpoint
                confidence = min(0.9, 0.5 + speedDifference / 1000) // 速度差异越大，置信度越高

                logger.info("速度差异明显(\(Int(speedDifference))ms)，选择更快端点: \(selectedEndpoint)")
            }
        }

        let isChina = selectedEndpoint == "sanvaCn"

        logger.info("最终决策: 端点=\(selectedEndpoint), 地区=\(isChina ? "中国" : "海外"), 置信度=\(confidence)")

        return (isChina, confidence, selectedEndpoint)
    }

    /// 检测语言偏好
    private func detectLanguagePreference() -> Double {
        let preferredLanguages = Locale.preferredLanguages
        let currentLocale = Locale.current.identifier

        // 检查首选语言中是否包含中文
        let hasChineseLanguage = preferredLanguages.contains { language in
            language.hasPrefix("zh") || language.contains("Hans") || language.contains("CN")
        }

        // 检查当前地区设置
        let isChineseLocale = currentLocale.contains("CN") || currentLocale.contains("Hans")

        if hasChineseLanguage && isChineseLocale {
            return 1.0
        } else if hasChineseLanguage {
            return 0.7
        } else if isChineseLocale {
            return 0.6
        } else {
            return 0.0
        }
    }

    /// 检测时区偏好
    private func detectTimezonePreference() -> Double {
        let timezone = TimeZone.current.identifier

        // 中国时区列表
        let chineseTimezones = [
            "Asia/Shanghai",
            "Asia/Chongqing",
            "Asia/Harbin",
            "Asia/Kashgar",
            "Asia/Urumqi",
            "Asia/Beijing"
        ]

        return chineseTimezones.contains(timezone) ? 1.0 : 0.0
    }
    

    
    /// 获取缓存的检测结果
    private func getCachedResult() -> RegionDetectionResult? {
        let cacheTimestamp = Preferences.regionCacheTimestamp.value
        let cachedIsChina = Preferences.cachedRegionResult.value

        // 检查缓存是否有效
        let cacheAge = Date().timeIntervalSince1970 - cacheTimestamp
        guard cacheAge < cacheValidityDuration else {
            logger.info("端点选择缓存已过期")
            return nil
        }

        logger.info("使用缓存的端点选择结果: \(cachedIsChina ? "中国" : "海外")")

        return RegionDetectionResult(
            isChina: cachedIsChina,
            confidence: 0.7, // 缓存结果的置信度稍低
            sources: ["缓存结果"],
            selectedEndpoint: cachedIsChina ? "sanvaCn" : "sanva",
            speedResults: [], // 缓存不包含详细的测速结果
            timestamp: Date(timeIntervalSince1970: cacheTimestamp)
        )
    }

    /// 缓存检测结果
    private func cacheResult(_ result: RegionDetectionResult) {
        Preferences.cachedRegionResult.value = result.isChina
        Preferences.regionCacheTimestamp.value = result.timestamp.timeIntervalSince1970
        logger.info("端点选择结果已缓存: \(result.selectedEndpoint)")
    }

    /// 清除缓存
    func clearCache() {
        Preferences.regionCacheTimestamp.value = 0
        logger.info("端点选择缓存已清除")
    }

    /// 手动触发重新检测
    func refreshDetection() async {
        let result = await detectOptimalEndpoint(forceRefresh: true)
        logger.info("手动重新检测完成: \(result.selectedEndpoint)")
    }
}
